# MCP Client and Server

A TypeScript project for building Model Context Protocol (MCP) clients and servers.

## Prerequisites

- Node.js 18 or higher
- npm

## Setup

1. Install dependencies:
```bash
npm install
```

## Development

### Available Scripts

- `npm run dev` - Compile TypeScript and run the server once
- `npm run dev:watch` - Watch for TypeScript changes and recompile automatically
- `npm run build` - Compile TypeScript to JavaScript
- `npm start` - Run the compiled JavaScript server
- `npm run clean` - Remove the dist directory
- `npm run watch` - Watch for changes and recompile automatically (alias for dev:watch)

### Development Workflow

1. **Quick development**: Use `npm run dev` to compile and run the server once
2. **Watch mode development**: Use `npm run dev:watch` to automatically recompile on file changes
3. **Production build**: Use `npm run build` to compile TypeScript to JavaScript
4. **Production run**: Use `npm start` to run the compiled JavaScript

**Recommended Development Flow:**
- Use `npm run dev:watch` in one terminal to watch for changes
- Use `npm start` in another terminal to run the server (restart manually after changes)
- Or use `npm run dev` for quick one-time compilation and run

### Project Structure

```
├── src/
│   └── server.ts          # Main server implementation
├── dist/                  # Compiled JavaScript output (generated)
├── tsconfig.json          # TypeScript configuration
├── package.json           # Project dependencies and scripts
└── .gitignore            # Git ignore rules
```

## TypeScript Configuration

The project is configured with strict TypeScript settings for better code quality:

- Strict null checks
- No implicit any
- No unused locals/parameters
- Exact optional property types
- And more strict checks

## MCP SDK

This project uses the `@modelcontextprotocol/sdk` package to work with the Model Context Protocol. The example server demonstrates:

- Basic server setup
- TypeScript type definitions
- Proper error handling
- Async/await patterns

## Next Steps

To extend this project:

1. Add resource handlers to your MCP server
2. Implement tool handlers
3. Add prompt handlers
4. Set up proper transport mechanisms (stdio, HTTP, etc.)
5. Add unit tests

## License

ISC
