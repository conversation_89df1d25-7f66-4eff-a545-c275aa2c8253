# MCP Client and Server

A TypeScript project for building Model Context Protocol (MCP) clients and servers.

## Prerequisites

- Node.js 18 or higher
- npm

## Setup

1. Install dependencies:
```bash
npm install
```

## Development

### Available Scripts

- `npm run dev` - Run the server in development mode with ts-node
- `npm run build` - Compile TypeScript to JavaScript
- `npm start` - Run the compiled JavaScript server
- `npm run clean` - Remove the dist directory
- `npm run watch` - Watch for changes and recompile automatically

### Development Workflow

1. **Development mode**: Use `npm run dev` to run TypeScript directly without compilation
2. **Production build**: Use `npm run build` to compile TypeScript to JavaScript
3. **Production run**: Use `npm start` to run the compiled JavaScript

### Project Structure

```
├── src/
│   └── server.ts          # Main server implementation
├── dist/                  # Compiled JavaScript output (generated)
├── tsconfig.json          # TypeScript configuration
├── package.json           # Project dependencies and scripts
└── .gitignore            # Git ignore rules
```

## TypeScript Configuration

The project is configured with strict TypeScript settings for better code quality:

- Strict null checks
- No implicit any
- No unused locals/parameters
- Exact optional property types
- And more strict checks

## MCP SDK

This project uses the `@modelcontextprotocol/sdk` package to work with the Model Context Protocol. The example server demonstrates:

- Basic server setup
- TypeScript type definitions
- Proper error handling
- Async/await patterns

## Next Steps

To extend this project:

1. Add resource handlers to your MCP server
2. Implement tool handlers
3. Add prompt handlers
4. Set up proper transport mechanisms (stdio, HTTP, etc.)
5. Add unit tests

## License

ISC
