{"name": "mcp-client-and-server", "version": "1.0.0", "description": "", "main": "dist/server.js", "scripts": {"build": "tsc", "start": "node dist/server.js", "dev": "ts-node src/server.ts", "clean": "rm -rf dist", "prebuild": "npm run clean", "watch": "tsc --watch", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@modelcontextprotocol/sdk": "^1.15.1"}, "devDependencies": {"@types/node": "^24.0.14", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}