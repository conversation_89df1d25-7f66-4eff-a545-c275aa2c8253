{"name": "mcp-client-and-server", "version": "1.0.0", "description": "", "main": "dist/server.js", "scripts": {"build": "tsc", "start": "node dist/server.js", "dev": "tsc && node dist/server.js", "dev:watch": "tsc --watch", "clean": "rm -rf dist", "prebuild": "npm run clean", "watch": "tsc --watch", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@modelcontextprotocol/sdk": "^1.15.1"}, "devDependencies": {"@types/node": "^24.0.14", "typescript": "^5.8.3"}}